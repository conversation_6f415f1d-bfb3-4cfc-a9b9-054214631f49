import { useCallback, useEffect, useState } from '@lynx-js/react'

import './App.css'
import arrow from './assets/arrow.png'
import lynxLogo from './assets/lynx-logo.png'
import reactLynxLogo from './assets/react-logo.png'

const API_KEY_STORAGE_KEY = 'openrouter_api_key'

export function App(props: {
  onMounted?: () => void
}) {
  const [alterLogo, setAlterLogo] = useState(false)
  const [prompt, setPrompt] = useState("")
  const [apiKey, setApiKey] = useState("")
  const [output, setOutput] = useState("")
  const [loading, setLoading] = useState(false)
  const [showApiKeyDialog, setShowApiKeyDialog] = useState(false)
  const [tempApiKey, setTempApiKey] = useState("")

  useEffect(() => {
    console.info('Hello, ReactLynx')
    // Load saved API key from localStorage
    const savedApiKey = localStorage.getItem(API_KEY_STORAGE_KEY)
    if (savedApiKey) {
      setApiKey(savedApiKey)
    }
    props.onMounted?.()
  }, [])

  const onTap = useCallback(() => {
    'background only'
    setAlterLogo(!alterLogo)
  }, [alterLogo])

  const onApiCall = useCallback(async () => {
    'background only'
    if (!apiKey.trim()) {
      setShowApiKeyDialog(true)
      return
    }

    if (!prompt.trim()) {
      setOutput("Please enter a prompt first!")
      return
    }

    setLoading(true)
    setOutput("")

    try {
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'openai/gpt-4o',
          messages: [
            {
              role: 'user',
              content: prompt,
            },
          ],
        }),
      })

      const data = await response.json()

      if (response.ok && data.choices && data.choices[0]) {
        setOutput(data.choices[0].message.content)
      } else {
        setOutput(`Error: ${data.error?.message || 'Unknown error occurred'}`)
      }
    } catch (err) {
      console.error("API call failed:", err)
      setOutput(`Network error: ${err instanceof Error ? err.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }, [apiKey, prompt])

  const saveApiKey = useCallback(() => {
    'background only'
    if (tempApiKey.trim()) {
      setApiKey(tempApiKey.trim())
      localStorage.setItem(API_KEY_STORAGE_KEY, tempApiKey.trim())
      setShowApiKeyDialog(false)
      setTempApiKey("")
    }
  }, [tempApiKey])

  const deleteApiKey = useCallback(() => {
    'background only'
    setApiKey("")
    localStorage.removeItem(API_KEY_STORAGE_KEY)
    setOutput("")
  }, [])

  const closeApiKeyDialog = useCallback(() => {
    'background only'
    setShowApiKeyDialog(false)
    setTempApiKey("")
  }, [])

  return (
    <view>
      <view className='Background' />
      <view className='App'>
        <view className='Banner'>
          <view className='Logo' bindtap={onTap}>
            {alterLogo
              ? <image src={reactLynxLogo} className='Logo--react' />
              : <image src={lynxLogo} className='Logo--lynx' />}
          </view>
          <text className='Title'>AI Chat App</text>
          <text className='Subtitle'>on Lynx</text>
        </view>

        <view className='Content' style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
          {/* API Key Management */}
          <view style={{ marginBottom: '20px' }}>
            <text style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '10px' }}>
              API Key Status: {apiKey ? '✅ Set' : '❌ Not Set'}
            </text>
            <view style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
              <view bindtap={() => setShowApiKeyDialog(true)}>
                <text style={{
                  backgroundColor: '#007bff',
                  color: 'white',
                  padding: '8px 16px',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}>
                  {apiKey ? 'Update API Key' : 'Set API Key'}
                </text>
              </view>
              {apiKey && (
                <view bindtap={deleteApiKey}>
                  <text style={{
                    backgroundColor: '#dc3545',
                    color: 'white',
                    padding: '8px 16px',
                    borderRadius: '4px',
                    cursor: 'pointer'
                  }}>
                    Delete API Key
                  </text>
                </view>
              )}
            </view>
          </view>

          {/* Prompt Input - Using a simple text display and button for now */}
          <view style={{ marginBottom: '20px' }}>
            <text style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '10px', display: 'block' }}>
              Current prompt:
            </text>
            <view style={{
              backgroundColor: '#f8f9fa',
              border: '1px solid #ccc',
              borderRadius: '4px',
              padding: '10px',
              minHeight: '60px',
              marginBottom: '10px'
            }}>
              <text style={{ fontSize: '14px' }}>
                {prompt || 'No prompt set. Click "Set Prompt" to enter your question.'}
              </text>
            </view>
            <view bindtap={() => {
              const newPrompt = window.prompt('Enter your prompt:', prompt || 'What is the meaning of life?')
              if (newPrompt !== null) {
                setPrompt(newPrompt)
              }
            }}>
              <text style={{
                backgroundColor: '#6c757d',
                color: 'white',
                padding: '8px 16px',
                borderRadius: '4px',
                cursor: 'pointer',
                display: 'inline-block'
              }}>
                Set Prompt
              </text>
            </view>
          </view>

          {/* Send Button */}
          <view style={{ marginBottom: '20px' }}>
            <view bindtap={onApiCall}>
              <text style={{
                backgroundColor: loading ? '#6c757d' : '#28a745',
                color: 'white',
                padding: '12px 24px',
                borderRadius: '4px',
                cursor: loading ? 'not-allowed' : 'pointer',
                fontSize: '16px',
                fontWeight: 'bold',
                display: 'inline-block'
              }}>
                {loading ? 'Sending...' : 'Send Message'}
              </text>
            </view>
          </view>

          {/* Output Display */}
          {output && (
            <view style={{ marginBottom: '20px' }}>
              <text style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '10px' }}>
                Response:
              </text>
              <view style={{
                backgroundColor: '#f8f9fa',
                border: '1px solid #dee2e6',
                borderRadius: '4px',
                padding: '15px',
                fontSize: '14px',
                lineHeight: '1.5'
              }}>
                <text>{output}</text>
              </view>
            </view>
          )}
        </view>

        <view style={{ flex: 1 }}></view>
      </view>

      {/* API Key Dialog */}
      {showApiKeyDialog && (
        <view style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <view style={{
            backgroundColor: 'white',
            padding: '30px',
            borderRadius: '8px',
            maxWidth: '500px',
            width: '90%',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
          }}>
            <text style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '15px', display: 'block' }}>
              Enter OpenRouter API Key
            </text>
            <text style={{ fontSize: '14px', color: '#666', marginBottom: '15px', display: 'block' }}>
              Get your API key from https://openrouter.ai/keys
            </text>
            <view style={{
              backgroundColor: '#f8f9fa',
              border: '1px solid #ccc',
              borderRadius: '4px',
              padding: '10px',
              marginBottom: '10px',
              minHeight: '40px'
            }}>
              <text style={{ fontSize: '14px' }}>
                {tempApiKey || 'Click "Enter API Key" to set your key'}
              </text>
            </view>
            <view style={{ marginBottom: '20px' }}>
              <view bindtap={() => {
                const newApiKey = window.prompt('Enter your OpenRouter API Key:', tempApiKey || '')
                if (newApiKey !== null) {
                  setTempApiKey(newApiKey)
                }
              }}>
                <text style={{
                  backgroundColor: '#28a745',
                  color: 'white',
                  padding: '8px 16px',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  display: 'block',
                  textAlign: 'center'
                }}>
                  Enter API Key
                </text>
              </view>
            </view>
            <view style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
              <view bindtap={closeApiKeyDialog}>
                <text style={{
                  backgroundColor: '#6c757d',
                  color: 'white',
                  padding: '8px 16px',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}>
                  Cancel
                </text>
              </view>
              <view bindtap={saveApiKey}>
                <text style={{
                  backgroundColor: '#007bff',
                  color: 'white',
                  padding: '8px 16px',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}>
                  Save
                </text>
              </view>
            </view>
          </view>
        </view>
      )}
    </view>
  )
}
